////////////////////////////////
// Engine
///////////////////////////////

// [mount_point]@[physical_path] - "/" will allow standard game file reading to use the path to the game files
// Otherwise it's "/APP_DATA", "/APP_DATA_RAW" etc, normally so that adhoc can refer to these mount points
// NOTE - kinda related: It appears that /appcount mounts both "/" and "/APP_CONT", so an external mod folder using dlc might work aswell
fsroot=/@/data/gtsport_fsroot/

// Exact:
disable_vegas
automation
reset_localsave
clear_resource_cache

// not sure
logger_<??>=1,0,1,0

// <key>=false/true or string value
okbutton
cleanup_tmp
cleanup_download
dev_dynamic_env_resoluiton_level
username
language
2160p
vrscale
fpc_file
occlusionculling_mode (cpucs)
vegas_portal
vegas_config
vegas_server
logger_net_server

////////////////////////////////
// SCRIPTS
///////////////////////////////
[BootEntry]
no_meter
grpdebug
bgmlib
sounddebug
machine_learning_mode
web_real_event
lanmode
use_specdb
specdb
show_savedata_state
disable_loaddata
disable_save_err_dlg
feature_release_level
start_project
boot_project

skip_op
enable_startup_setting
disable_playgo
spotid
dev_photo_capture
debug_setting - Enables settings debug settings
autodemo_entry_num
enable_debug_entry
debug_entry_time
debug_entry
normal_entry
host_priority
automation
username
forceEnablePostRaceScene
lobby_host
no_package
adhoc_trace_object
resident_overlay
branch
postRaceSceneBGAlias
prefs
beta
goldenmaster
music_rally_event_id
autodemo_return_time
system_info
GoldTrophyAlias
runviewer_menu
reset_localsave
daily_auto_entry_index
dr_debug
sr_debug
current_time
birthday
dart
playgo
pfs_version
voice_chat_test
mapview_open
rtext_debug
demo_idx

// >1.00 (seen in 1.25)
emotional