////////////////////////////////
// Engine
///////////////////////////////

// [mount_point]@[physical_path] - "/" will allow standard game file reading to use the path to the game files
// Otherwise it's "/APP_DATA", "/APP_DATA_RAW" etc, normally so that adhoc can refer to these mount points
// NOTE - kinda related: It appears that /appcount mounts both "/" and "/APP_CONT", so an external mod folder using dlc might work aswell
fsroot=/@/data/gtsport_fsroot/

////////////////////////////////
// PROJECT SCRIPTS
///////////////////////////////

[Boot]
skip_op - Skips opening
op - ?
enable_startup_setting - ?
delay_capture - ?

[ARCADE]
bot_arcade_game_mode
bot_arcade_game_opt
bot_arcade_course_layout
dart
min_color
arcade_all_car
vr_ui_debug

[CarSelect]
bot_arcade_car_list
bot_arcade_car_color
bot_arcade_driving_assist

[RaceVrTour]
entries

[Garage]
livery_copy_thumbnail

[Online]
duplicate_lobby_rooms
bot
nobothost
bot_mode
botroom
bot_event_index
bot_user_index
bot_car
bot_type
open
bot_auto_pilot

[SystemStatic]
top_scapes_id
top_scapes_debug
hide_debug_notifications
interim_for_ppr_demo
using_local_scripts

[RaceSport]
bot

[BrandCentral]
force_enable_scapes_dlc
skip_check_scapes_hidden_spot_list
specdb

[TopBg]
top_museum_debug
top_scene
top_scene_car
top_replay_notice_debug
interim_for_ppr_demo
top_car_slideshow_debug

[Race]
entries
dev_photo_capture

[Scapes]
force_enable_scapes_dlc
skip_check_scapes_hidden_spot_list

[Dialog]
dart

[OverlayMenu2]
open

[Components]
open
quick_menu_debug
design_debug

[Livery2]
livery_json_path
livery_debug
livery_edit_full
livery_copy_file
livery_skip_confirm
livery_immediate_save
livery_copy_path
livery_new_feature

[Photo]
force_enable_scapes_dlc
skip_check_scapes_hidden_spot_list
dev_photo_capture
dev_scapes_advanced
enable_scapes_restore_old_data
disable_scapes_check_disk_size
photo_metadata_debug
username

[Sport]
open
current_time
championship_valid_race_count
debug_entry
bot_mode
bot_event_index

////////////////////////////////
// SCRIPTS
///////////////////////////////
[BootEntry]
no_meter
grpdebug
bgmlib
sounddebug
grim
grim_debug
clear_cache
lanmode
server_special_value
specdb
disable_loaddata
online_save
disable_save_err_dlg
feature_release_level
start_project
boot_project

[Garage]
thumbnail_capture_debug
username

[Gallery]
username

[Inventory]
livery_debug

[Grim]
ranking_upload
riso

[Logger]
online_debug

[Network]
pfs_version
mpss_debug
mpss_env
mpss_cluster
voice_chat_test
no_eula

[Main]
resident_overlay
latency_test
nobot
bot
prefs
prefs_overwrite
using_local_scripts
demo_idx
branch
rtext_debug
no_package
adhoc_trace_object

[Behavior]
behavior.userdir

[MissionRace]
bot_auto_drive

[AutoDemo]
start_project

[DailyRace]
bot
bot_auto_drive
voice_chat_test
override_dr

[OnlineRace]
bot
bot_auto_drive
voice_chat_test
override_dr

[OnlineLobby]
bot
bot_auto_drive
voice_chat_test
override_dr

[LanBattle/PointRace/OnlineRoom]
bot
bot_auto_drive
voice_chat_test
override_dr

[SingleRace]
username
bot_auto_drive
enable_highspeed_loading

[RaceFunctions]
enable_highspeed_loading
bot_auto_drive
username

[PointRacePractice/DailyRacePractice]
enable_highspeed_loading
bot_auto_drive
username
online_debug
debug_entry

[RaceSequence]
no_wall_recovery
quiet_guide_normal
single_ray_surface_search
multi_ray_surface_search
multi_ray_surface_search2
behavior.beta
using_local_scripts
laps
player_pos
enable_highspeed_loading
bot_auto_drive
username

[Util]
specdb
demo_course
delivery_check_interval
delivery_path
delivery_mode
levelup_debug
dr_debug
sr_debug
current_time
birthday
dart
playgo
machine_learning_mode