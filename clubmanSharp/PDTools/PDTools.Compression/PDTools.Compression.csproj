<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>netstandard2.0</TargetFrameworks>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ImpromptuNinjas.ZStd" Version="*******" />
    <PackageReference Include="SharpZipLib" Version="1.3.2" />
    <PackageReference Include="Syroot.BinaryData" Version="5.2.2" />
    <PackageReference Include="Syroot.BinaryData.Memory" Version="5.2.2" />
  </ItemGroup>

</Project>
