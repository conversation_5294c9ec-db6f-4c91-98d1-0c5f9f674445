<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>netstandard2.0;netcoreapp2.1</TargetFrameworks>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <Optimize>false</Optimize>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="SharpZipLib" Version="1.3.2" />
    <PackageReference Include="Syroot.BinaryData" Version="5.2.2" />
    <PackageReference Include="Syroot.BinaryData.Memory" Version="5.2.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PDTools.Compression\PDTools.Compression.csproj" />
    <ProjectReference Include="..\PDTools.Crypto\PDTools.Crypto.csproj" />
  </ItemGroup>

</Project>
