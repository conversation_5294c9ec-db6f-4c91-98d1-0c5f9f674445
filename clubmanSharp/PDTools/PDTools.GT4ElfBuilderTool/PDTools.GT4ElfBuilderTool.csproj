<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="SharpZipLib" Version="1.4.0" />
    <PackageReference Include="Syroot.BinaryData" Version="5.2.2" />
    <PackageReference Include="Syroot.BinaryData.Memory" Version="5.2.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PDTools.Compression\PDTools.Compression.csproj" />
    <ProjectReference Include="..\PDTools.Crypto\PDTools.Crypto.csproj" />
  </ItemGroup>

</Project>
