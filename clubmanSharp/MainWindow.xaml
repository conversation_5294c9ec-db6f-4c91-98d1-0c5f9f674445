﻿<Window x:Class="ClubmanSharp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ClubmanSharp"
        mc:Ignorable="d"
        Title="ClubmanSharp" Height="400" Width="500" MinHeight="400" MinWidth="500" MaxHeight="400" MaxWidth="500">
    <Grid Background="#222">
        <Grid.Resources>
            <Style x:Key="{x:Type Hyperlink}" TargetType="{x:Type Hyperlink}">
                <Setter Property="Foreground" Value="#4f9fff"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Foreground" Value="Red"/>
                    </Trigger>
                    <Trigger Property="IsEnabled" Value="True">
                        <Setter Property="Cursor" Value="Hand"/>
                    </Trigger>
                </Style.Triggers>
            </Style>
        </Grid.Resources>
        <TabControl Name="tabControl" Background="#222">
            <TabItem Header="Startup">
                <Grid Background="#222">
                    <TextBlock Name="TxtHeader" Text="ClubmanSharp by ddm" Foreground="#DDD" HorizontalAlignment="Left" VerticalAlignment="Top" Height="40" Width="460" FontSize="24" Margin="10,10,0,0"/>
                    <TextBlock Name="TxtWikiLink" Foreground="#DDD" HorizontalAlignment="Left" VerticalAlignment="Top" Height="40" Width="460" FontSize="24" Margin="10,50,0,0">
                        <Hyperlink NavigateUri="https://github.com/ddm999/ClubmanSharp/wiki" RequestNavigate="Hyperlink_Click">Click to view usage guide and car tunes</Hyperlink>
                    </TextBlock>
                    <TextBlock Name="TxtDetails" Text="" TextWrapping="Wrap" Foreground="#DDD" HorizontalAlignment="Left" VerticalAlignment="Top" Height="220" Width="460" FontSize="16" Margin="10,100,0,0"/>
                    <TextBlock Name="TxtSrcLink" Foreground="#DDD" HorizontalAlignment="Left" VerticalAlignment="Top" Height="40" Width="460" FontSize="16" Margin="10,300,0,0">
                        <Hyperlink NavigateUri="https://github.com/ddm999/ClubmanSharp" RequestNavigate="Hyperlink_Click">Source available at github.com/ddm999/ClubmanSharp</Hyperlink>
                    </TextBlock>
                    <Button Name="BtnPatchedRemotePlay" FontFamily="Roboto" Background="#666" Foreground="#DDD" Click="BtnPatchedRemotePlay_Click" FontSize="16" Content="Install Patched Remote Play" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Margin="10,0,0,65" HorizontalAlignment="Left" Width="240" Height="24" VerticalAlignment="Bottom"/>
                    <TextBlock Name="TxtUpdateLink" Visibility="Hidden" Foreground="#DDD" HorizontalAlignment="Left" VerticalAlignment="Top" Height="40" Width="460" FontSize="16" Margin="10,300,0,0">
                        <Hyperlink NavigateUri="https://github.com/ddm999/ClubmanSharp/releases" RequestNavigate="Hyperlink_Click">Update to latest at github.com/ddm999/ClubmanSharp/releases</Hyperlink>
                    </TextBlock>
                </Grid>
            </TabItem>
            <TabItem Header="Race">
                <Grid Background="#222">
                    <TextBlock Name="TxtShortHelp" Text="" TextWrapping="Wrap" Foreground="#DDD" FontSize="14" Margin="10,10,0,0" HorizontalAlignment="Left" Width="440" Height="214" VerticalAlignment="Top"/>
                    <Button Name="BtnStartStop" FontFamily="Roboto" Background="#666" Foreground="#DDD" Click="StartStop_Click" FontSize="24" Content="Start" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Margin="0,0,10,10" HorizontalAlignment="Right" Width="100" Height="40" VerticalAlignment="Bottom"/>
                    <TextBox Name="TxtIP" TextChanged="TxtIP_TextChanged" FontFamily="Roboto Mono" Background="#222" Foreground="#DDD" FontSize="24" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" HorizontalAlignment="Right" Margin="0,0,115,10" TextWrapping="NoWrap" Text="x" Width="355" Height="40" VerticalAlignment="Bottom"/>
                    <TextBlock Name="TxtLap" Text="Fastest Lap: x:xx.xx" HorizontalAlignment="Right" Margin="0,0,10,85" TextWrapping="Wrap" Foreground="#DDD" VerticalAlignment="Bottom" Height="25" Width="220" FontSize="16"/>
                    <TextBlock Name="TxtCredits" Text="Estimated Credits: 0" HorizontalAlignment="Right" Margin="0,0,10,55" TextWrapping="Wrap" Foreground="#DDD" VerticalAlignment="Bottom" Height="25" Width="220" FontSize="16"/>
                    <TextBlock Name="TxtState" Text="Current State: Unknown" HorizontalAlignment="Right" Margin="0,0,250,85" TextWrapping="Wrap" Foreground="#DDD" VerticalAlignment="Bottom" Height="25" Width="220" FontSize="16"/>
                    <TextBlock Name="TxtRaces" Text="Completed Races: 0" HorizontalAlignment="Right" Margin="0,0,250,55" TextWrapping="Wrap" Foreground="#DDD" VerticalAlignment="Bottom" Height="25" Width="220" FontSize="16"/>
                </Grid>
            </TabItem>
            <TabItem Header="Settings">
                <Grid Background="#222">
                    <Grid>
                        <TextBlock Name="TxtCar" Visibility="Hidden" Text="Used Tune:" Foreground="#DDD" FontSize="16" Margin="10,10,0,0" HorizontalAlignment="Left" VerticalAlignment="Top"/>
                        <!--
                        <RadioButton Name="RadioCarAuto" IsChecked="True" Content="Auto" Foreground="#DDD" FontSize="16" Margin="110,12,0,0" HorizontalAlignment="Left" VerticalAlignment="Top" VerticalContentAlignment="Center"/>
                        -->
                        <RadioButton Name="RadioCarGTO" Visibility="Hidden" Checked="RadioCarGTO_Checked" IsChecked="False" Content="GTO" Foreground="#DDD" FontSize="16" Margin="180,12,0,0" HorizontalAlignment="Left" VerticalAlignment="Top" VerticalContentAlignment="Center"/>
                        <RadioButton Name="RadioCarWRX" Visibility="Hidden" Checked="RadioCarWRX_Checked" IsChecked="False" Content="WRX" Foreground="#DDD" FontSize="16" Margin="240,12,0,0" HorizontalAlignment="Left" VerticalAlignment="Top" VerticalContentAlignment="Center"/>
                        <!--
                        <RadioButton Name="RadioCarR34" IsChecked="False" Content="R34" Foreground="#DDD" FontSize="16" Margin="300,12,0,0" HorizontalAlignment="Left" VerticalAlignment="Top" VerticalContentAlignment="Center"/>
                        -->
                    </Grid>
                    <Grid>
                        <TextBlock Name="TxtDelay" Text="Menu Delay Settings:" Foreground="#DDD" FontSize="16" Margin="10,40,0,0" HorizontalAlignment="Left" VerticalAlignment="Top"/>
                        <RadioButton Name="RadioDelayPS4" Checked="RadioDelayPS4_Checked" IsChecked="False" Content="PS4" Foreground="#DDD" FontSize="16" Margin="180,42,0,0" HorizontalAlignment="Left" VerticalAlignment="Top" VerticalContentAlignment="Center"/>
                        <RadioButton Name="RadioDelayPS5" Checked="RadioDelayPS5_Checked" IsChecked="False" Content="PS5" Foreground="#DDD" FontSize="16" Margin="240,42,0,0" HorizontalAlignment="Left" VerticalAlignment="Top" VerticalContentAlignment="Center"/>
                        <RadioButton Name="RadioDelayCustom" Checked="RadioDelayCustom_Checked" IsChecked="False" Content="Custom" Foreground="#DDD" FontSize="16" Margin="300,42,0,0" HorizontalAlignment="Left" VerticalAlignment="Top" VerticalContentAlignment="Center"/>
                        <TextBlock Name="TxtCustomDelay" Text="Custom Delays (ms):" Foreground="#DDD" FontSize="16" Margin="10,70,0,0" HorizontalAlignment="Left" VerticalAlignment="Top"/>
                        <TextBlock Name="TxtCustomDelayShort" Text="Short:" Foreground="#DDD" FontSize="16" Margin="180,70,0,0" HorizontalAlignment="Left" VerticalAlignment="Top"/>
                        <TextBox Name="CustomDelayShort" IsEnabled="False" TextChanged="CustomDelayShort_TextChanged" Text="500" Width="52" Foreground="#DDD" Background="#333" FontSize="16" Margin="230,70,0,0" HorizontalAlignment="Left" VerticalAlignment="Top"/>
                        <TextBlock Name="TxtCustomDelayLong" Text="Long:" Foreground="#DDD" FontSize="16" Margin="290,70,0,0" HorizontalAlignment="Left" VerticalAlignment="Top"/>
                        <TextBox Name="CustomDelayLong" IsEnabled="False" TextChanged="CustomDelayLong_TextChanged" Text="3000" Width="52" Foreground="#DDD" Background="#333" FontSize="16" Margin="340,70,0,0" HorizontalAlignment="Left" VerticalAlignment="Top"/>
                    </Grid>
                    <Grid>
                        <TextBlock Name="TxtThrottle" Text="Max Throttle (Laps 3-5)" Foreground="#DDD" FontSize="16" Margin="10,100,0,0" HorizontalAlignment="Left" VerticalAlignment="Top"/>
                        <TextBlock Name="TxtThrottleValue" Text="255" Foreground="#DDD" FontSize="16" Margin="430,100,0,0" HorizontalAlignment="Left" VerticalAlignment="Top"/>
                        <Slider Name="SliderThrottle" ValueChanged="SliderThrottle_ValueChanged"  Minimum="200" Maximum="255" Value="240" Width="240" Height="20" Margin="180,103,0,0"  HorizontalAlignment="Left" VerticalAlignment="Top" ToolTip="255/255"/>
                    </Grid>
                    <Grid>
                        <TextBlock Name="TxtConfirmButton" Text="Confirm Button:" Foreground="#DDD" FontSize="16" Margin="10,130,0,0" HorizontalAlignment="Left" VerticalAlignment="Top"/>
                        <RadioButton Name="RadioConfirmCross" Checked="RadioConfirmCross_Checked" IsChecked="False" Content="Cross (X)" Foreground="#DDD" FontSize="16" Margin="180,132,0,0" HorizontalAlignment="Left" VerticalAlignment="Top" VerticalContentAlignment="Center"/>
                        <RadioButton Name="RadioConfirmCircle" Checked="RadioConfirmCircle_Checked" IsChecked="False" Content="Circle (O)" Foreground="#DDD" FontSize="16" Margin="280,132,0,0" HorizontalAlignment="Left" VerticalAlignment="Top" VerticalContentAlignment="Center"/>
                    </Grid>
                    <Grid>
                        <TextBlock Name="TxtAutoRetry" Text="After packet drop failure:" Foreground="#DDD" FontSize="16" Margin="10,160,0,0" HorizontalAlignment="Left" VerticalAlignment="Top"/>
                        <RadioButton Name="RadioAutoRetryOff" Checked="RadioAutoRetryOff_Checked" IsChecked="False" Content="Error" Foreground="#DDD" FontSize="16" Margin="205,162,0,0" HorizontalAlignment="Left" VerticalAlignment="Top" VerticalContentAlignment="Center"/>
                        <RadioButton Name="RadioAutoRetryOn" Checked="RadioAutoRetryOn_Checked" IsChecked="False" Content="Retry" Foreground="#DDD" FontSize="16" Margin="280,162,0,0" HorizontalAlignment="Left" VerticalAlignment="Top" VerticalContentAlignment="Center"/>
                    </Grid>
                    <Grid>
                        <TextBlock Name="TxtCheckOverride" Text="Override event-specific checks:" Foreground="#F66" FontSize="16" Margin="10,0,0,70" HorizontalAlignment="Left" VerticalAlignment="Bottom"/>
                        <CheckBox Name="CheckOverrides" Checked="CheckOverrides_Checked" Unchecked="CheckOverrides_Unchecked" IsChecked="False" Content="Active" Foreground="#DDD" FontSize="16" Margin="245,0,0,68" HorizontalAlignment="Left" VerticalAlignment="Bottom"/>
                    </Grid>
                    <Grid>
                        <TextBlock Name="TxtDebugLog" Text="Debug log settings (only use when requested):" Foreground="#FA6" FontSize="16" Margin="10,0,0,40" HorizontalAlignment="Left" Height="21" VerticalAlignment="Bottom"/>
                        <CheckBox Name="CheckMain" Checked="CheckDebugMain_Checked" Unchecked="CheckDebugMain_Unchecked" Content="MainWindow / handlers" Foreground="#DDD" FontSize="16" Margin="10,0,0,10" VerticalAlignment="Bottom" HorizontalAlignment="Left" Width="190"/>
                        <CheckBox Name="CheckMenu" Checked="CheckDebugMenu_Checked" Unchecked="CheckDebugMenu_Unchecked" Content="MenuBot" Foreground="#DDD" FontSize="16" Margin="205,0,0,10" VerticalAlignment="Bottom" HorizontalAlignment="Left" Width="90"/>
                        <CheckBox Name="CheckDriv" Checked="CheckDebugDriv_Checked" Unchecked="CheckDebugDriv_Unchecked" Content="DriverBot" Foreground="#DDD" FontSize="16" Margin="295,0,0,10" VerticalAlignment="Bottom" HorizontalAlignment="Left" Width="90"/>
                        <CheckBox Name="CheckTrck" Checked="CheckDebugTrck_Checked" Unchecked="CheckDebugTrck_Unchecked" Content="TrackData" Foreground="#DDD" FontSize="16" Margin="385,0,0,10" VerticalAlignment="Bottom" HorizontalAlignment="Left" Width="90"/>
                    </Grid>
                </Grid>
            </TabItem>
            <TabItem Header="Licensing">
                <Grid Background="#222">
                    <TextBlock Name="TxtLicensing" Text="" TextWrapping="Wrap" Foreground="#DDD" HorizontalAlignment="Left" VerticalAlignment="Top" Height="350" Width="460" FontSize="12" Margin="10,5,0,0"/>
                </Grid>
            </TabItem>
            <TabItem Name="debugTabItem" Header="Debug">
                <TextBlock Name="TxtDebug" Text="" TextWrapping="Wrap" Foreground="#DDD" HorizontalAlignment="Left" VerticalAlignment="Top" Height="320" Width="460" FontSize="12" Margin="10,10,0,0"/>
            </TabItem>
        </TabControl>
    </Grid>
</Window>
