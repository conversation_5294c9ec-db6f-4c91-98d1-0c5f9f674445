﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="ClubmanSharp" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="ip" Type="System.String" Scope="User">
      <Value Profile="(Default)">************</Value>
    </Setting>
    <Setting Name="customLongDelay" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">3000</Value>
    </Setting>
    <Setting Name="customShortDelay" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">500</Value>
    </Setting>
    <Setting Name="delaySetting" Type="System.Byte" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="carSetting" Type="System.Byte" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="maxThrottle" Type="System.Byte" Scope="User">
      <Value Profile="(Default)">255</Value>
    </Setting>
    <Setting Name="confirmButton" Type="System.Byte" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="autoRetry" Type="System.Byte" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="debugLog" Type="System.Byte" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="debugMenu" Type="System.Byte" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="debugDriv" Type="System.Byte" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="debugTrck" Type="System.Byte" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="trackOverrides" Type="System.Byte" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
  </Settings>
</SettingsFile>