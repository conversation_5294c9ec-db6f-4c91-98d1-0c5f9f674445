﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="PDTools\**" />
    <EmbeddedResource Remove="PDTools\**" />
    <None Remove="PDTools\**" />
    <Page Remove="PDTools\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Nefarius.ViGEm.Client" Version="1.21.230" />
    <PackageReference Include="NuGet.Versioning" Version="6.3.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="PDTools\PDTools.SimulatorInterface\PDTools.SimulatorInterface.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Syroot.BinaryData">
      <HintPath>..\PDTools\PDTools.SimulatorInterface\bin\Release\net6.0\Syroot.BinaryData.dll</HintPath>
    </Reference>
    <Reference Include="Syroot.BinaryData.Core">
      <HintPath>..\PDTools\PDTools.SimulatorInterface\bin\Release\net6.0\Syroot.BinaryData.Core.dll</HintPath>
    </Reference>
    <Reference Include="Syroot.BinaryData.Memory">
      <HintPath>..\PDTools\PDTools.SimulatorInterface\bin\Release\net6.0\Syroot.BinaryData.Memory.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Settings.Designer.cs">
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <None Update="Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>

</Project>
