[workspace]
resolver = "2"
members = [
    "clubman-sharp-rust",    # 主应用程序
    "rust-vgamepad",         # 虚拟游戏手柄库 (参考Python vgamepad)
    "gt7-telemetry",         # GT7遥测数据库 (参考GT7 telemetry Python库)
]

[workspace.dependencies]
# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# UI框架
slint = "1.3"

# 网络通信
reqwest = { version = "0.11", features = ["json"] }

# 配置管理
config = "0.14"
toml = "0.8"

# 日志
log = "0.4"
env_logger = "0.10"

# 二进制数据处理
byteorder = "1.5"
bytes = "1.5"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# 数学计算
nalgebra = "0.32"

# 构建依赖
[workspace.dependencies.slint-build]
version = "1.3"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1