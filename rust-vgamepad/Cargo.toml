[package]
name = "rust-vgamepad"
version = "0.1.0"
edition = "2021"
authors = ["ClubmanSharp Contributors"]
description = "虚拟游戏手柄库 - 参考Python vgamepad，支持Windows和macOS"
license = "MIT"

[dependencies]
# 工作空间依赖
anyhow = { workspace = true }
thiserror = { workspace = true }
log = { workspace = true }
byteorder = { workspace = true }

# Windows平台依赖 (ViGEm)
[target.'cfg(windows)'.dependencies]
windows = { version = "0.52", features = [
    "Win32_Foundation",
    "Win32_System_LibraryLoader",
    "Win32_System_Threading",
    "Win32_System_Console"
] }

# macOS平台依赖 (IOKit HID)
[target.'cfg(target_os = "macos")'.dependencies]
core-foundation = "0.9"
io-kit-sys = "0.4"

[lib]
name = "rust_vgamepad"
crate-type = ["lib"]